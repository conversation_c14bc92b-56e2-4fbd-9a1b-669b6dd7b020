# Multi-Variant Document Classification Implementation Guide

## Overview
This guide shows how to configure the document classifier to handle 5-6 document variants effectively.

## Current System Capabilities

The existing `Classify.py` already supports multiple variants through:

1. **Multiple Document Types**: Each config can define multiple document types
2. **Section-Based Classification**: header, body, footer, big_font sections
3. **Client-Specific Configurations**: Different clients have variant-specific configs
4. **Fallback Mechanism**: Default return when no variants match

## Implementation Strategies

### Strategy 1: Single Configuration with Multiple Variants

```json
{
    "document_types": {
        "variant_1": { "return": "variant_1_de.json", ... },
        "variant_2": { "return": "variant_2_de.json", ... },
        "variant_3": { "return": "variant_3_de.json", ... },
        "variant_4": { "return": "variant_4_de.json", ... },
        "variant_5": { "return": "variant_5_de.json", ... },
        "variant_6": { "return": "variant_6_de.json", ... }
    }
}
```

### Strategy 2: Hierarchical Classification

```json
{
    "document_types": {
        "primary_type_old": {
            "return": "old_form_de.json",
            "header": {
                "include_strings": ["OLD FORM", "VERSION 1.0"],
                "exclude_strings": ["NEW", "REVISED"]
            }
        },
        "primary_type_new": {
            "return": "new_form_de.json", 
            "header": {
                "include_strings": ["NEW FORM", "REVISED", "VERSION 2.0"],
                "exclude_strings": ["OLD"]
            }
        }
    }
}
```

### Strategy 3: Feature-Based Classification

Use different sections to identify variants:

- **Header-based**: Different titles/headers
- **Body-based**: Different content sections
- **Font-based**: Different typography patterns
- **Layout-based**: Different positioning

## Key Configuration Parameters

### 1. Include/Exclude Strings
```json
"include_strings": [
    "SPECIFIC VARIANT TEXT",
    "UNIQUE IDENTIFIER",
    "VERSION NUMBER"
],
"exclude_strings": [
    "OTHER VARIANT TEXT",
    "CONFLICTING TERMS"
]
```

### 2. Big Font Detection
```json
"big_font": {
    "include_strings": ["TITLE TEXT"],
    "exclude_strings": ["FOOTER"],
    "height_threshold": 0.6,
    "num_clusters": 3
}
```

### 3. Position-Based Parameters
```json
"max_upper_block": 3,
"use_upper_split_percentage": 0.45,
"max_lines_for_header": 5
```

## Best Practices for 5-6 Variants

### 1. Identify Unique Characteristics
For each variant, identify:
- Unique text strings
- Different layouts
- Specific formatting
- Version numbers/dates

### 2. Use Hierarchical Approach
```
Main Document Type
├── Variant 1 (Old Format)
├── Variant 2 (New Format) 
├── Variant 3 (Digital)
├── Variant 4 (Short Form)
├── Variant 5 (Multilingual)
└── Variant 6 (State-Specific)
```

### 3. Test Classification Order
The classifier processes variants in order, so:
- Put most specific variants first
- Put general variants last
- Use exclude_strings to prevent false positives

### 4. Confidence Scoring
Implement confidence scoring to:
- Rank variant matches
- Handle ambiguous cases
- Provide fallback options

## Implementation Steps

### Step 1: Analyze Your Documents
1. Collect samples of all 5-6 variants
2. Identify unique text patterns for each
3. Note layout differences
4. Document version indicators

### Step 2: Create Classification Config
1. Define include_strings for each variant
2. Set exclude_strings to prevent conflicts
3. Configure section-specific rules
4. Set appropriate thresholds

### Step 3: Test and Refine
1. Test with sample documents
2. Check classification accuracy
3. Adjust thresholds and strings
4. Handle edge cases

### Step 4: Deploy and Monitor
1. Deploy configuration
2. Monitor classification results
3. Collect feedback
4. Iterate and improve

## Example Usage

```python
from docvu_de_core.policy_classifier.Classify import Classify

# Load multi-variant configuration
config_path = "path/to/multi_variant_config.json"
classifier = Classify(config_path)

# Process document
result = classifier.process_form("path/to/document.json")

print(f"Document Type: {result['document_type']}")
print(f"Variant: {result.get('section', 'unknown')}")
print(f"Starting Page: {result.get('starting_page', -1)}")
```

## Troubleshooting

### Common Issues:
1. **Multiple variants matching**: Use more specific include/exclude strings
2. **No variants matching**: Check default_return configuration
3. **Wrong variant selected**: Adjust classification order and exclusions
4. **Low accuracy**: Refine text patterns and thresholds

### Debug Mode:
Enable debug mode to see classification details:
```json
"debug": true
```

This will print matching statistics for troubleshooting.
